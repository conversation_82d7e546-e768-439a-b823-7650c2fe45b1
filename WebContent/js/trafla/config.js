var showHotels = true	;
var customTimeAllow = false;
var useGivenAirportOnly = true;
var MAX_NUM_MONTHS_FOR_SEARCH = 9;
var MAX_NUM_DAYS_FOR_SEARCH = 331;
var bizTravelBaseUrl = "https://beta.routespring.com/dev-api";
var plaidEnvironment = "sandbox";
var stripePublicKey = "pk_test_XRQK86fo6wOs9bslgvktAQsc";
var originUrl = (location.origin.indexOf("localhost")>-1)?"https://beta.routespring.com":location.origin;
var stripAllowed = false;
var commonAnswerMap={};
	commonAnswerMap["45 minutes or less before departure"]="1";
	commonAnswerMap["60 to 90 minutes before departure"]="2";
	commonAnswerMap["90 minutes or more before departure"]="3";
	commonAnswerMap["40 minutes or less before departure"]="1";
	commonAnswerMap["40 to 80 minutes before departure"]="2";
	commonAnswerMap["80 minutes or more before departure"]="3";
	
	commonAnswerMap["I drive and park my car"]="1";
	commonAnswerMap["I take a cab"]="2";
	commonAnswerMap["I take public transportation"]="3";
	commonAnswerMap["Other"]="4";
	
	commonAnswerMap["Maybe"]="1";
	commonAnswerMap["Never"]="2";
	commonAnswerMap["There is only one airport near my home"]="3";
	
	 
	commonAnswerMap["I prefer them when I have a long flight"]="1";
	commonAnswerMap["I'll fly them if I have to"]="2";
	commonAnswerMap["I avoid them at all costs"]="3";
	
	commonAnswerMap["Fly the night before, and get there in time for dinner"]="1";
	commonAnswerMap["Fly the night before, and get in late"]="2";
	commonAnswerMap["Wake up early, and arrive in the morning"]="3";
	
	commonAnswerMap["If the price is significantly lower"]="1";
	commonAnswerMap["If it's my preferred airline"]="2";
	commonAnswerMap["If the timing is best"]="3";
	commonAnswerMap["If there are no direct flights"]="4";
	
var commonAnswersReverseMap = {
		early_catch_domestic_flight : {
			"1":"45 minutes or less before departure",
			"2":"60 to 90 minutes before departure",
			"3":"90 minutes or more before departure"
		},
		mode_to_airport:{
			"1":"I drive and park my car",
			"2":"I take a cab",
			"3":"I take public transportation",
			"4":"Other"
			
		},
		would_choose_another_airport:{
			"1":"Maybe",
			"2":"Never",
			"3":"There is only one airport near my home"
		},
		red_eye_flight:{
			"1":"I prefer them when I have a long flight",
			"2":"I'll fly them if I have to",
			"3":"I avoid them at all costs"
		},
		pref_meeting_time:{
			"1":"Fly the night before, and get there in time for dinner",
			"2":"Fly the night before, and get in late",
			"3":"Wake up early, and arrive in the morning"
		},
		layover_flights:{
			"1":"If the price is significantly lower",
			"2":"If it's my preferred airline",
			"3":"If the timing is best",
			"4":"If there are no direct flights"
		}
}

const settings = {
	maxflightSearchAllowed :5,
    createExpenseUrl: bizTravelBaseUrl + "/AddToExpensify/createExpense",
    mailToUrl: bizTravelBaseUrl + "/WebUserRequestHandler/mailTo",
    searchCombo: "https://wb2wpmp877.execute-api.us-east-1.amazonaws.com/dev/test?",
    addTobeta: bizTravelBaseUrl + '/data/addToBeta',
    fetchDiscounts: bizTravelBaseUrl + "/data/fetchDiscounts",
    fetchHotelChains: bizTravelBaseUrl + '/data/fetchHotelsChain',
    impersonateUrl : bizTravelBaseUrl+"/Admin/ImpersonateLoggedOnUser",
    impersonateLogoutUrl : bizTravelBaseUrl+"/Admin/RevertToSelf",
    search: bizTravelBaseUrl + "/FlightSearch?search=all",
    hotelSearch:bizTravelBaseUrl+"/hotel/gethotel",
    hotelDescription:bizTravelBaseUrl+"/hotel/description",
    hotelCancelTicketUrl:bizTravelBaseUrl+"/hotel/cancelTicket",
    flightIssueTicket:bizTravelBaseUrl+"/BookFlight/flightIssueTicket",
    hotelAmeneties:bizTravelBaseUrl+"/hotel/getHotelAmeneties",
    hotelRecheckPrice:bizTravelBaseUrl+"/hotel/reCheckHotelPrice",
    hotelCommentUrl:bizTravelBaseUrl+"/hotel/getHotelComment",
    hotelRoolRule:bizTravelBaseUrl+"/hotel/getHotelRoomRule",
    hotelRoomRuleByHotelList:bizTravelBaseUrl+"/hotel/getHotelRoomRuleList",
    // search: "flight-data.php?",
    farerules: bizTravelBaseUrl + "/BookFlight/farerule?fareCode=",
    tripdetails: bizTravelBaseUrl + "/BookFlight/tripdetails?uuid=",
    initOptions:bizTravelBaseUrl+"/ValidateAndAuthenticateV2/initOptions",
    syncCalendar:bizTravelBaseUrl+"/ValidateAndAuthenticateV2/syncCalendar",
    sTokenUrl:bizTravelBaseUrl+"/ValidateAndAuthenticateV2/generateSecretToken",
    autocompleteHotelChain:bizTravelBaseUrl+"/autocomplete/gethotelChain",
    autocompleteAirport:bizTravelBaseUrl+"/autocomplete/airport",
    autocompleteHotelName:bizTravelBaseUrl+"/autocomplete/hotelName",
    tripDetailsPage: "flight-booked.html",
    generateTicketByTicketIdURL: bizTravelBaseUrl + "/Admin/generateTicketByTicketId",
    generateUserInfoForBooking: bizTravelBaseUrl + "/Admin/generateUserInfoForBooking",
    generateHotelTicketByReservationId: bizTravelBaseUrl + "/Admin/generateHotelTicketByReservationId",
    
    getAirlineSeat:bizTravelBaseUrl+"/BookFlight/getAirlineSeat",
    
    flightCancelTicketUrl:bizTravelBaseUrl+"/BookFlight/flightCancelTicket",
    flightVoidTicketUrl:bizTravelBaseUrl+"/BookFlight/voidTicket",
    initUrl: bizTravelBaseUrl + "/BookFlight/init",
    urlEndPoint:bizTravelBaseUrl +"/data/saveOtherUserPref",
    revalidateUrl: bizTravelBaseUrl + "/BookFlight/revalidate",
    revalidateCarUrl : bizTravelBaseUrl + "/car/fetchRules",
    bookingUrl:bizTravelBaseUrl + "/BookFlight/bookingTicket",
    retryPaymentUrl:bizTravelBaseUrl + "/BookFlight/paymentRetry",
    agentEmailPreview:bizTravelBaseUrl + "/AgentServlet/viewResponse",
    agentEmailRespond:bizTravelBaseUrl + "/AgentServlet/respond",
    agentEmailDraft:bizTravelBaseUrl + "/AgentServlet/draftResponse",
    agentNLPStatus: bizTravelBaseUrl + "/AgentServlet/tryNlp",    
    agentNotifyConfirmedBook: bizTravelBaseUrl + "/AgentServlet/notifyConfirmBook",
    agentSearchResults: bizTravelBaseUrl + "/AgentServlet/agentSearchResults",
    agentUserTripCopyOption:bizTravelBaseUrl + "/AgentServlet/copyOption",
    agentListUserTrips:bizTravelBaseUrl + "/AgentServlet/listUserTrips",
    agentListTripsEvents:bizTravelBaseUrl + "/AgentServlet/listTripEvents",
    agentSaveUserTrip:bizTravelBaseUrl + "/AgentServlet/saveUserTrip",
    agentCreateUserTrip:bizTravelBaseUrl + "/AgentServlet/createUserTrip",
    agentUserTripItinerary:bizTravelBaseUrl + "/AgentServlet/userTripItinerary",
    agentUserTripQuotation:bizTravelBaseUrl + "/AgentServlet/userTripQuotation",
    agentUserTripContextEmail:bizTravelBaseUrl + "/AgentServlet/userTripContextEmail",
    agentUserTripExpensifyEmail:bizTravelBaseUrl + "/AgentServlet/userTripExpensifyEmail",
    agentUserTripCancellationEmail:bizTravelBaseUrl + "/AgentServlet/userTripCancellationEmail",
    agentUserTripTransaction:bizTravelBaseUrl + "/AgentServlet/userTripGenerateTransaction",
    ticketDetailsURL:bizTravelBaseUrl + "/Admin/ticketDetails",
    bookingDetailsUrl: bizTravelBaseUrl +"/TripDetails/getUserBookings",
    confirmedTransactionsUrl: bizTravelBaseUrl +"/AgentServlet/listUserConfirmations",
    approveTransactionUrl: bizTravelBaseUrl +"/AgentServlet/approveUserConfirmTransaction",
    saveTransactionUrl: bizTravelBaseUrl +"/AgentServlet/saveUserConfirmTransaction",
    cancelTransactionUrl: bizTravelBaseUrl +"/AgentServlet/cancelUserConfirmTransaction",
	tripDetailsUrl: bizTravelBaseUrl + "/BookFlight/tripdetails",
	loginUrl: bizTravelBaseUrl +  '/data/loginUser',
	confirmBookUrl: bizTravelBaseUrl +  '/data/getConfirmBookData?',
    preferences:bizTravelBaseUrl + '/data/fetchPreferences/flight?',
    corpTravelPolicies:bizTravelBaseUrl + '/data/getCompanyData?',
	payment:bizTravelBaseUrl + '/data/fetchPreferences/payment?',
	profile:bizTravelBaseUrl + '/data/fetchPreferences/profile?',
	gallopCash:bizTravelBaseUrl + '/data/getGallopCash?',
	otherUserInfoURL: bizTravelBaseUrl+"/data/fetchPreferences/otherUserInfo",
	save_preference:bizTravelBaseUrl + '/data/savePreferences/',
	bookHotel : bizTravelBaseUrl + "/hotel/bookHotel" ,
	search_sync: "https://wb2wpmp877.execute-api.us-east-1.amazonaws.com/dev/combosync?",
	book_now: "https://wb2wpmp877.execute-api.us-east-1.amazonaws.com/dev/bookflight",
	trafla_users: "https://wb2wpmp877.execute-api.us-east-1.amazonaws.com/dev/user",
	betaUrl: bizTravelBaseUrl + "/data/checkBetaUser?",
	getHTML: "https://fdbr2xtz6b.execute-api.us-east-1.amazonaws.com/dev/flights/html?",
	validateAuthUrl:bizTravelBaseUrl + "/ValidateAndAuthenticateV2",
	transferTicketUrl: bizTravelBaseUrl + "/AgentServlet/transferTicket",
	emailBookFlow:"WEB",// WEB,EMAIL
	iataToZoneMapUrl: bizTravelBaseUrl + "/api/getIataZoneMap?",
	createConsoleLinkURL: bizTravelBaseUrl + "/AgentServlet/getConsoleUrl",
	companies: bizTravelBaseUrl + "/AgentServlet/companies",
	listCompanyPolicies: bizTravelBaseUrl + "/AgentServlet/listCompanyPolicies",
	companyPolicy: bizTravelBaseUrl + "/AgentServlet/saveCompanyPolicy",
	discountByCompany: bizTravelBaseUrl + "/AgentServlet/discountByCompany",
	carSearch: bizTravelBaseUrl + "/car/searchCars",
	issuingUrl: bizTravelBaseUrl + "/issuing/createStripeBankAccountToken",
	plaidInitUrl: bizTravelBaseUrl + "/issuing/initiatePlaid",
	reconcileReportUrl:bizTravelBaseUrl + "/AgentServlet/reconcileReport",
	transferUrl: bizTravelBaseUrl + "/issuing/transfer",
	resetDemoAccount: bizTravelBaseUrl + "/AgentServlet/resetDemoAccount",
	deleteDemoAccount: bizTravelBaseUrl + "/AgentServlet/deleteDemoAccount",
	fetchTravelCreditsForUser: bizTravelBaseUrl + "/AgentServlet/getUserCredits",
	updateTravelCreditsRecord: bizTravelBaseUrl + "/AgentServlet/updateUserCredits",
	importEmployeesUrl:bizTravelBaseUrl + "/AgentServlet/importNewEmployeeCSV",
	importCreditsUrl:bizTravelBaseUrl + "/AgentServlet/importCreditsCSV",
	approval_request_url :bizTravelBaseUrl + "/AgentServlet/sendApprovalRequest",
	listIssuingStatusUrl :bizTravelBaseUrl + "/AgentServlet/listIssuingStatus",
	updateIssuingStatusUrl :bizTravelBaseUrl + "/AgentServlet/updateIssuingStatus",
	urlForCreateOnboardingDef : bizTravelBaseUrl + "/onboarding/createItem",
	urlForEditOnboardingDef : bizTravelBaseUrl + "/onboarding/editItem",
	urlForFetchOnboardingDef : bizTravelBaseUrl + "/onboarding/getItems",
	urlForDeleteOnboardingDef : bizTravelBaseUrl + "/onboarding/deleteItem",
	urlForCreateUpdatesDef : bizTravelBaseUrl + "/updates/createItem",
	urlForEditUpdatesDef : bizTravelBaseUrl + "/updates/editItem",
	urlForFetchUpdatesDef : bizTravelBaseUrl + "/updates/getItems",
	urlForDeleteUpdatesDef : bizTravelBaseUrl + "/updates/deleteItem",
	urlForInsertTravelCredits: bizTravelBaseUrl + "/AgentServlet/insertTravelCredits",
	urlForSavePaymentDetails: bizTravelBaseUrl + "/AgentServlet/savePaymentDetails",
	urlForFetchCompanyFeaturesDef : bizTravelBaseUrl + "/companyfeatures/getItems",
	urlForEditCompanyFeaturesDef : bizTravelBaseUrl + "/companyfeatures/editItem",
	urlForFetchSearchHandlerCompanyDef : bizTravelBaseUrl + "/companyHandler/getItems",
	urlForEditSearchHandlerCompanyDef : bizTravelBaseUrl + "/companyHandler/editItem",
	urlForOTALink: bizTravelBaseUrl + "/AgentServlet/openOTAforImpersonatedUser",
	urlForSamlIdpConfig: bizTravelBaseUrl +"/AgentServlet/getAllSaml",
	urlForSamlIdpConfigEdit : bizTravelBaseUrl + "/AgentServlet/editSaml",
	getTags: bizTravelBaseUrl +"/tagsv2/tags/get",
	urlForTravellers: bizTravelBaseUrl+"/api/company/travellers",
	urlForAdjustmentTransaction: bizTravelBaseUrl+"/AgentServlet/userTripGenerateAdjustmentTransaction",
	urlForUpdateAdjustmentTransaction: bizTravelBaseUrl+"/AgentServlet/userTripUpdateAdjustmentTransaction",
	urlForUploadAdjustmentTransactionFile: bizTravelBaseUrl+"/AgentServlet/uploadAdjustmentTransactionFile",
	urlForNotifyAdjustment:bizTravelBaseUrl+"/AgentServlet/notifyAdjustment",
	urlForFetchBrandFareAttributes: bizTravelBaseUrl+ "/brandFareAttributes/getItems",
	urlForEditBrandFareAttributes: bizTravelBaseUrl+ "/brandFareAttributes/editItem",
	urlForCreateBrandFareAttributes: bizTravelBaseUrl+ "/brandFareAttributes/createItem",
	urlForRemoveBrandFareAttributes: bizTravelBaseUrl+ "/brandFareAttributes/deleteItem",
	urlForStripeRefund: bizTravelBaseUrl+"/AgentServlet/stripRefund",
	urlForAddConfermaDetail : bizTravelBaseUrl+"/AgentServlet/authorsizedConferma",
	urlForUpdateConfermaDetail : bizTravelBaseUrl+ "/AgentServlet/updateConferma",
	urlForAddBookingInfo: bizTravelBaseUrl+ '/AgentServlet/addBookingInfo',
	reconReportUrl: bizTravelBaseUrl+ '/ReconHelperServlet/getReportData',
	reconReportRefreshDataUrl: bizTravelBaseUrl+ '/ReconHelperServlet/refreshData',
	adjustAPIUrl: bizTravelBaseUrl+ '/ReconHelperServlet/generateAdjustmentForRecon',
	ignoreAPIUrl: bizTravelBaseUrl+ '/ReconHelperServlet/ignoreTicket',
	confermaDetails: bizTravelBaseUrl + '/AgentServlet/getConfermaDetails',
	updatePassengerTypeAndFare: bizTravelBaseUrl+'/AgentServlet/updatePassengerTypeAndFare',
	urlForFetchFinancialContacts: bizTravelBaseUrl+ "/financialContacts/getItems",
	urlForCreateFinancialContacts: bizTravelBaseUrl+ "/financialContacts/createItem",
	urlForRemoveFinancialContacts: bizTravelBaseUrl+ "/financialContacts/deleteItem",
	urlForledgerRecon: bizTravelBaseUrl+ "/ReconHelperServlet/getLedgerReconData",
	urlForGettingEmailIdsFromCompanyId: bizTravelBaseUrl +'/financialContacts/getEmailsByCompanyId',
	urlForFetchingUserTripDetailLogs: bizTravelBaseUrl + '/AgentServlet/getUserTripLogs',
	urlForFetchCompanyAnalytics: bizTravelBaseUrl + '/AgentServlet/getCompanyAnalylics',
	utlForFetchCompanyAnalyticsWsdr: bizTravelBaseUrl + '/AgentServlet/getCompanyAnalylicsWSDR',
}
const type_speed = 50;
var chat_url ="/chat-01.html?";

if(localStorage.getItem('api') != "undefined") {
	var api = localStorage.getItem('api');
	chat_url = chat_url+"&api="+api;
}
if(localStorage.getItem('bookflight') != "undefined") {
	var bookflight = localStorage.getItem('bookflight');
	chat_url = chat_url+"&bookflight="+bookflight;
}
if(localStorage.getItem('output') != "undefined") {
	var api = localStorage.getItem('output');
	var output = localStorage.getItem('output');
	chat_url = chat_url+"&output="+output;
}
const urls_config = {
	search_template: '../template/search-template.html',
	trip_template: 'TripDetail.html',
    airline_url    : "/airline-preferences.html",
    hotel_url      : "/hotel-preferences.html",
    chat_url       : chat_url,
    payment_url    : "/payment-information.html",
    preferences_url: "/preferences.html",
    login_url	   : "/index.html",
    search_template_v2: '../template/search-template-v2.html',
}

const google_credentials ={
	clientid: '************-p42rqrg3dbm5achnsp2i5lqehu6ark77.apps.googleusercontent.com',
	api_key: 'AIzaSyA3lywR0fqq2uX6zwfiHpoRxah_zETrSPc'
}
original_alert_function = window.alert ;
alert = function(mesg) {
	console.trace(mesg);
}

// Not being used in latest code

const emailAddressTipText = "All email addresses specified here will be linked to your account and can be used to request travel booking on email.";
const homeAddressTipText = "This will be the address where travel originates from in non-working hours.";
const workAddressTipText = "This will be the address where travel originates from in working hours.";
const phoneAddressTipText = "Optional";
const tsaPreIdTipText = "If you are a U.S. citizen currently enrolled in any of the U.S. Customs and Border Protection Trusted Traveler programs (Global Entry, NEXUS or SENTRI), please provide your Known Traveler Number or Pass ID.";

window.NREUM||(NREUM={}),__nr_require=function(t,n,e){function r(e){if(!n[e]){var o=n[e]={exports:{}};t[e][0].call(o.exports,function(n){var o=t[e][1][n];return r(o||n)},o,o.exports)}return n[e].exports}if("function"==typeof __nr_require)return __nr_require;for(var o=0;o<e.length;o++)r(e[o]);return r}({1:[function(t,n,e){function r(t){try{s.console&&console.log(t)}catch(n){}}var o,i=t("ee"),a=t(15),s={};try{o=localStorage.getItem("__nr_flags").split(","),console&&"function"==typeof console.log&&(s.console=!0,o.indexOf("dev")!==-1&&(s.dev=!0),o.indexOf("nr_dev")!==-1&&(s.nrDev=!0))}catch(c){}s.nrDev&&i.on("internal-error",function(t){r(t.stack)}),s.dev&&i.on("fn-err",function(t,n,e){r(e.stack)}),s.dev&&(r("NR AGENT IN DEVELOPMENT MODE"),r("flags: "+a(s,function(t,n){return t}).join(", ")))},{}],2:[function(t,n,e){function r(t,n,e,r,o){try{d?d-=1:i("err",[o||new UncaughtException(t,n,e)])}catch(s){try{i("ierr",[s,c.now(),!0])}catch(u){}}return"function"==typeof f&&f.apply(this,a(arguments))}function UncaughtException(t,n,e){this.message=t||"Uncaught error with no additional information",this.sourceURL=n,this.line=e}function o(t){i("err",[t,c.now()])}var i=t("handle"),a=t(16),s=t("ee"),c=t("loader"),f=window.onerror,u=!1,d=0;c.features.err=!0,t(1),window.onerror=r;try{throw new Error}catch(l){"stack"in l&&(t(8),t(7),"addEventListener"in window&&t(5),c.xhrWrappable&&t(9),u=!0)}s.on("fn-start",function(t,n,e){u&&(d+=1)}),s.on("fn-err",function(t,n,e){u&&(this.thrown=!0,o(e))}),s.on("fn-end",function(){u&&!this.thrown&&d>0&&(d-=1)}),s.on("internal-error",function(t){i("ierr",[t,c.now(),!0])})},{}],3:[function(t,n,e){t("loader").features.ins=!0},{}],4:[function(t,n,e){function r(t){}if(window.performance&&window.performance.timing&&window.performance.getEntriesByType){var o=t("ee"),i=t("handle"),a=t(8),s=t(7),c="learResourceTimings",f="addEventListener",u="resourcetimingbufferfull",d="bstResource",l="resource",p="-start",h="-end",m="fn"+p,w="fn"+h,v="bstTimer",y="pushState",g=t("loader");g.features.stn=!0,t(6);var b=NREUM.o.EV;o.on(m,function(t,n){var e=t[0];e instanceof b&&(this.bstStart=g.now())}),o.on(w,function(t,n){var e=t[0];e instanceof b&&i("bst",[e,n,this.bstStart,g.now()])}),a.on(m,function(t,n,e){this.bstStart=g.now(),this.bstType=e}),a.on(w,function(t,n){i(v,[n,this.bstStart,g.now(),this.bstType])}),s.on(m,function(){this.bstStart=g.now()}),s.on(w,function(t,n){i(v,[n,this.bstStart,g.now(),"requestAnimationFrame"])}),o.on(y+p,function(t){this.time=g.now(),this.startPath=location.pathname+location.hash}),o.on(y+h,function(t){i("bstHist",[location.pathname+location.hash,this.startPath,this.time])}),f in window.performance&&(window.performance["c"+c]?window.performance[f](u,function(t){i(d,[window.performance.getEntriesByType(l)]),window.performance["c"+c]()},!1):window.performance[f]("webkit"+u,function(t){i(d,[window.performance.getEntriesByType(l)]),window.performance["webkitC"+c]()},!1)),document[f]("scroll",r,{passive:!0}),document[f]("keypress",r,!1),document[f]("click",r,!1)}},{}],5:[function(t,n,e){function r(t){for(var n=t;n&&!n.hasOwnProperty(u);)n=Object.getPrototypeOf(n);n&&o(n)}function o(t){s.inPlace(t,[u,d],"-",i)}function i(t,n){return t[1]}var a=t("ee").get("events"),s=t(18)(a,!0),c=t("gos"),f=XMLHttpRequest,u="addEventListener",d="removeEventListener";n.exports=a,"getPrototypeOf"in Object?(r(document),r(window),r(f.prototype)):f.prototype.hasOwnProperty(u)&&(o(window),o(f.prototype)),a.on(u+"-start",function(t,n){var e=t[1],r=c(e,"nr@wrapped",function(){function t(){if("function"==typeof e.handleEvent)return e.handleEvent.apply(e,arguments)}var n={object:t,"function":e}[typeof e];return n?s(n,"fn-",null,n.name||"anonymous"):e});this.wrapped=t[1]=r}),a.on(d+"-start",function(t){t[1]=this.wrapped||t[1]})},{}],6:[function(t,n,e){var r=t("ee").get("history"),o=t(18)(r);n.exports=r,o.inPlace(window.history,["pushState","replaceState"],"-")},{}],7:[function(t,n,e){var r=t("ee").get("raf"),o=t(18)(r),i="equestAnimationFrame";n.exports=r,o.inPlace(window,["r"+i,"mozR"+i,"webkitR"+i,"msR"+i],"raf-"),r.on("raf-start",function(t){t[0]=o(t[0],"fn-")})},{}],8:[function(t,n,e){function r(t,n,e){t[0]=a(t[0],"fn-",null,e)}function o(t,n,e){this.method=e,this.timerDuration=isNaN(t[1])?0:+t[1],t[0]=a(t[0],"fn-",this,e)}var i=t("ee").get("timer"),a=t(18)(i),s="setTimeout",c="setInterval",f="clearTimeout",u="-start",d="-";n.exports=i,a.inPlace(window,[s,"setImmediate"],s+d),a.inPlace(window,[c],c+d),a.inPlace(window,[f,"clearImmediate"],f+d),i.on(c+u,r),i.on(s+u,o)},{}],9:[function(t,n,e){function r(t,n){d.inPlace(n,["onreadystatechange"],"fn-",s)}function o(){var t=this,n=u.context(t);t.readyState>3&&!n.resolved&&(n.resolved=!0,u.emit("xhr-resolved",[],t)),d.inPlace(t,y,"fn-",s)}function i(t){g.push(t),h&&(x?x.then(a):w?w(a):(E=-E,O.data=E))}function a(){for(var t=0;t<g.length;t++)r([],g[t]);g.length&&(g=[])}function s(t,n){return n}function c(t,n){for(var e in t)n[e]=t[e];return n}t(5);var f=t("ee"),u=f.get("xhr"),d=t(18)(u),l=NREUM.o,p=l.XHR,h=l.MO,m=l.PR,w=l.SI,v="readystatechange",y=["onload","onerror","onabort","onloadstart","onloadend","onprogress","ontimeout"],g=[];n.exports=u;var b=window.XMLHttpRequest=function(t){var n=new p(t);try{u.emit("new-xhr",[n],n),n.addEventListener(v,o,!1)}catch(e){try{u.emit("internal-error",[e])}catch(r){}}return n};if(c(p,b),b.prototype=p.prototype,d.inPlace(b.prototype,["open","send"],"-xhr-",s),u.on("send-xhr-start",function(t,n){r(t,n),i(n)}),u.on("open-xhr-start",r),h){var x=m&&m.resolve();if(!w&&!m){var E=1,O=document.createTextNode(E);new h(a).observe(O,{characterData:!0})}}else f.on("fn-end",function(t){t[0]&&t[0].type===v||a()})},{}],10:[function(t,n,e){function r(t){var n=this.params,e=this.metrics;if(!this.ended){this.ended=!0;for(var r=0;r<d;r++)t.removeEventListener(u[r],this.listener,!1);if(!n.aborted){if(e.duration=a.now()-this.startTime,4===t.readyState){n.status=t.status;var i=o(t,this.lastSize);if(i&&(e.rxSize=i),this.sameOrigin){var c=t.getResponseHeader("X-NewRelic-App-Data");c&&(n.cat=c.split(", ").pop())}}else n.status=0;e.cbTime=this.cbTime,f.emit("xhr-done",[t],t),s("xhr",[n,e,this.startTime])}}}function o(t,n){var e=t.responseType;if("json"===e&&null!==n)return n;var r="arraybuffer"===e||"blob"===e||"json"===e?t.response:t.responseText;return h(r)}function i(t,n){var e=c(n),r=t.params;r.host=e.hostname+":"+e.port,r.pathname=e.pathname,t.sameOrigin=e.sameOrigin}var a=t("loader");if(a.xhrWrappable){var s=t("handle"),c=t(11),f=t("ee"),u=["load","error","abort","timeout"],d=u.length,l=t("id"),p=t(14),h=t(13),m=window.XMLHttpRequest;a.features.xhr=!0,t(9),f.on("new-xhr",function(t){var n=this;n.totalCbs=0,n.called=0,n.cbTime=0,n.end=r,n.ended=!1,n.xhrGuids={},n.lastSize=null,p&&(p>34||p<10)||window.opera||t.addEventListener("progress",function(t){n.lastSize=t.loaded},!1)}),f.on("open-xhr-start",function(t){this.params={method:t[0]},i(this,t[1]),this.metrics={}}),f.on("open-xhr-end",function(t,n){"loader_config"in NREUM&&"xpid"in NREUM.loader_config&&this.sameOrigin&&n.setRequestHeader("X-NewRelic-ID",NREUM.loader_config.xpid)}),f.on("send-xhr-start",function(t,n){var e=this.metrics,r=t[0],o=this;if(e&&r){var i=h(r);i&&(e.txSize=i)}this.startTime=a.now(),this.listener=function(t){try{"abort"===t.type&&(o.params.aborted=!0),("load"!==t.type||o.called===o.totalCbs&&(o.onloadCalled||"function"!=typeof n.onload))&&o.end(n)}catch(e){try{f.emit("internal-error",[e])}catch(r){}}};for(var s=0;s<d;s++)n.addEventListener(u[s],this.listener,!1)}),f.on("xhr-cb-time",function(t,n,e){this.cbTime+=t,n?this.onloadCalled=!0:this.called+=1,this.called!==this.totalCbs||!this.onloadCalled&&"function"==typeof e.onload||this.end(e)}),f.on("xhr-load-added",function(t,n){var e=""+l(t)+!!n;this.xhrGuids&&!this.xhrGuids[e]&&(this.xhrGuids[e]=!0,this.totalCbs+=1)}),f.on("xhr-load-removed",function(t,n){var e=""+l(t)+!!n;this.xhrGuids&&this.xhrGuids[e]&&(delete this.xhrGuids[e],this.totalCbs-=1)}),f.on("addEventListener-end",function(t,n){n instanceof m&&"load"===t[0]&&f.emit("xhr-load-added",[t[1],t[2]],n)}),f.on("removeEventListener-end",function(t,n){n instanceof m&&"load"===t[0]&&f.emit("xhr-load-removed",[t[1],t[2]],n)}),f.on("fn-start",function(t,n,e){n instanceof m&&("onload"===e&&(this.onload=!0),("load"===(t[0]&&t[0].type)||this.onload)&&(this.xhrCbStart=a.now()))}),f.on("fn-end",function(t,n){this.xhrCbStart&&f.emit("xhr-cb-time",[a.now()-this.xhrCbStart,this.onload,n],n)})}},{}],11:[function(t,n,e){n.exports=function(t){var n=document.createElement("a"),e=window.location,r={};n.href=t,r.port=n.port;var o=n.href.split("://");!r.port&&o[1]&&(r.port=o[1].split("/")[0].split("@").pop().split(":")[1]),r.port&&"0"!==r.port||(r.port="https"===o[0]?"443":"80"),r.hostname=n.hostname||e.hostname,r.pathname=n.pathname,r.protocol=o[0],"/"!==r.pathname.charAt(0)&&(r.pathname="/"+r.pathname);var i=!n.protocol||":"===n.protocol||n.protocol===e.protocol,a=n.hostname===document.domain&&n.port===e.port;return r.sameOrigin=i&&(!n.hostname||a),r}},{}],12:[function(t,n,e){function r(){}function o(t,n,e){return function(){return i(t,[f.now()].concat(s(arguments)),n?null:this,e),n?void 0:this}}var i=t("handle"),a=t(15),s=t(16),c=t("ee").get("tracer"),f=t("loader"),u=NREUM;"undefined"==typeof window.newrelic&&(newrelic=u);var d=["setPageViewName","setCustomAttribute","setErrorHandler","finished","addToTrace","inlineHit","addRelease"],l="api-",p=l+"ixn-";a(d,function(t,n){u[n]=o(l+n,!0,"api")}),u.addPageAction=o(l+"addPageAction",!0),u.setCurrentRouteName=o(l+"routeName",!0),n.exports=newrelic,u.interaction=function(){return(new r).get()};var h=r.prototype={createTracer:function(t,n){var e={},r=this,o="function"==typeof n;return i(p+"tracer",[f.now(),t,e],r),function(){if(c.emit((o?"":"no-")+"fn-start",[f.now(),r,o],e),o)try{return n.apply(this,arguments)}finally{c.emit("fn-end",[f.now()],e)}}}};a("setName,setAttribute,save,ignore,onEnd,getContext,end,get".split(","),function(t,n){h[n]=o(p+n)}),newrelic.noticeError=function(t){"string"==typeof t&&(t=new Error(t)),i("err",[t,f.now()])}},{}],13:[function(t,n,e){n.exports=function(t){if("string"==typeof t&&t.length)return t.length;if("object"==typeof t){if("undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer&&t.byteLength)return t.byteLength;if("undefined"!=typeof Blob&&t instanceof Blob&&t.size)return t.size;if(!("undefined"!=typeof FormData&&t instanceof FormData))try{return JSON.stringify(t).length}catch(n){return}}}},{}],14:[function(t,n,e){var r=0,o=navigator.userAgent.match(/Firefox[\/\s](\d+\.\d+)/);o&&(r=+o[1]),n.exports=r},{}],15:[function(t,n,e){function r(t,n){var e=[],r="",i=0;for(r in t)o.call(t,r)&&(e[i]=n(r,t[r]),i+=1);return e}var o=Object.prototype.hasOwnProperty;n.exports=r},{}],16:[function(t,n,e){function r(t,n,e){n||(n=0),"undefined"==typeof e&&(e=t?t.length:0);for(var r=-1,o=e-n||0,i=Array(o<0?0:o);++r<o;)i[r]=t[n+r];return i}n.exports=r},{}],17:[function(t,n,e){n.exports={exists:"undefined"!=typeof window.performance&&window.performance.timing&&"undefined"!=typeof window.performance.timing.navigationStart}},{}],18:[function(t,n,e){function r(t){return!(t&&t instanceof Function&&t.apply&&!t[a])}var o=t("ee"),i=t(16),a="nr@original",s=Object.prototype.hasOwnProperty,c=!1;n.exports=function(t,n){function e(t,n,e,o){function nrWrapper(){var r,a,s,c;try{a=this,r=i(arguments),s="function"==typeof e?e(r,a):e||{}}catch(f){l([f,"",[r,a,o],s])}u(n+"start",[r,a,o],s);try{return c=t.apply(a,r)}catch(d){throw u(n+"err",[r,a,d],s),d}finally{u(n+"end",[r,a,c],s)}}return r(t)?t:(n||(n=""),nrWrapper[a]=t,d(t,nrWrapper),nrWrapper)}function f(t,n,o,i){o||(o="");var a,s,c,f="-"===o.charAt(0);for(c=0;c<n.length;c++)s=n[c],a=t[s],r(a)||(t[s]=e(a,f?s+o:o,i,s))}function u(e,r,o){if(!c||n){var i=c;c=!0;try{t.emit(e,r,o,n)}catch(a){l([a,e,r,o])}c=i}}function d(t,n){if(Object.defineProperty&&Object.keys)try{var e=Object.keys(t);return e.forEach(function(e){Object.defineProperty(n,e,{get:function(){return t[e]},set:function(n){return t[e]=n,n}})}),n}catch(r){l([r])}for(var o in t)s.call(t,o)&&(n[o]=t[o]);return n}function l(n){try{t.emit("internal-error",n)}catch(e){}}return t||(t=o),e.inPlace=f,e.flag=a,e}},{}],ee:[function(t,n,e){function r(){}function o(t){function n(t){return t&&t instanceof r?t:t?c(t,s,i):i()}function e(e,r,o,i){if(!l.aborted||i){t&&t(e,r,o);for(var a=n(o),s=h(e),c=s.length,f=0;f<c;f++)s[f].apply(a,r);var d=u[y[e]];return d&&d.push([g,e,r,a]),a}}function p(t,n){v[t]=h(t).concat(n)}function h(t){return v[t]||[]}function m(t){return d[t]=d[t]||o(e)}function w(t,n){f(t,function(t,e){n=n||"feature",y[e]=n,n in u||(u[n]=[])})}var v={},y={},g={on:p,emit:e,get:m,listeners:h,context:n,buffer:w,abort:a,aborted:!1};return g}function i(){return new r}function a(){(u.api||u.feature)&&(l.aborted=!0,u=l.backlog={})}var s="nr@context",c=t("gos"),f=t(15),u={},d={},l=n.exports=o();l.backlog=u},{}],gos:[function(t,n,e){function r(t,n,e){if(o.call(t,n))return t[n];var r=e();if(Object.defineProperty&&Object.keys)try{return Object.defineProperty(t,n,{value:r,writable:!0,enumerable:!1}),r}catch(i){}return t[n]=r,r}var o=Object.prototype.hasOwnProperty;n.exports=r},{}],handle:[function(t,n,e){function r(t,n,e,r){o.buffer([t],r),o.emit(t,n,e)}var o=t("ee").get("handle");n.exports=r,r.ee=o},{}],id:[function(t,n,e){function r(t){var n=typeof t;return!t||"object"!==n&&"function"!==n?-1:t===window?0:a(t,i,function(){return o++})}var o=1,i="nr@id",a=t("gos");n.exports=r},{}],loader:[function(t,n,e){function r(){if(!x++){var t=b.info=NREUM.info,n=l.getElementsByTagName("script")[0];if(setTimeout(u.abort,3e4),!(t&&t.licenseKey&&t.applicationID&&n))return u.abort();f(y,function(n,e){t[n]||(t[n]=e)}),c("mark",["onload",a()+b.offset],null,"api");var e=l.createElement("script");e.src="https://"+t.agent,n.parentNode.insertBefore(e,n)}}function o(){"complete"===l.readyState&&i()}function i(){c("mark",["domContent",a()+b.offset],null,"api")}function a(){return E.exists&&performance.now?Math.round(performance.now()):(s=Math.max((new Date).getTime(),s))-b.offset}var s=(new Date).getTime(),c=t("handle"),f=t(15),u=t("ee"),d=window,l=d.document,p="addEventListener",h="attachEvent",m=d.XMLHttpRequest,w=m&&m.prototype;NREUM.o={ST:setTimeout,SI:d.setImmediate,CT:clearTimeout,XHR:m,REQ:d.Request,EV:d.Event,PR:d.Promise,MO:d.MutationObserver};var v=""+location,y={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net",agent:"js-agent.newrelic.com/nr-1044.min.js"},g=m&&w&&w[p]&&!/CriOS/.test(navigator.userAgent),b=n.exports={offset:s,now:a,origin:v,features:{},xhrWrappable:g};t(12),l[p]?(l[p]("DOMContentLoaded",i,!1),d[p]("load",r,!1)):(l[h]("onreadystatechange",o),d[h]("onload",r)),c("mark",["firstbyte",s],null,"api");var x=0,E=t(17)},{}]},{},["loader",2,10,4,3]);
;NREUM.info={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net",licenseKey:"f22cf34f56",applicationID:"32606045",sa:1}





