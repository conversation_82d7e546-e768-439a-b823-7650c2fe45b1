<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offers API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Offers API Integration Test</h1>

    <div class="test-section">
        <h2>Configuration Test</h2>
        <button onclick="testConfig()">Test Config</button>
        <div id="configResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>API Endpoints Test</h2>
        <button onclick="testListOffers()">Test GET /offers</button>
        <button onclick="testGetOffer()">Test GET /offers/{id}</button>
        <button onclick="testCreateOffer()">Test POST /offers</button>
        <div id="apiResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>Form Population Test</h2>
        <button onclick="testFormPopulation()">Test Form Population</button>
        <div id="formResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>Loading State Test</h2>
        <button onclick="testLoadingState()">Test Loading State</button>
        <div id="loadingResult" class="test-result"></div>
    </div>

    <script src="../../js/trafla/config.js"></script>
    <script>
        function getBaseUrl() {
            return bizTravelBaseUrl || 'http://localhost:8080/TraflaBeta';
        }

        function testConfig() {
            const result = document.getElementById('configResult');
            const baseUrl = getBaseUrl();

            result.innerHTML = `
                <div class="info">
                    <strong>Base URL:</strong> ${baseUrl}<br>
                    <strong>Config loaded:</strong> ${typeof bizTravelBaseUrl !== 'undefined' ? 'Yes' : 'No'}<br>
                    <strong>Expected endpoints:</strong><br>
                    • GET ${baseUrl}/offers<br>
                    • GET ${baseUrl}/offers/{offerId}<br>
                    • POST ${baseUrl}/offers<br>
                    • PUT ${baseUrl}/offers/{offerId}<br>
                    • DELETE ${baseUrl}/offers/{offerId}
                </div>
            `;
        }

        async function testListOffers() {
            const result = document.getElementById('apiResult');
            result.innerHTML = '<div class="info">Testing GET /offers...</div>';

            try {
                const response = await fetch(`${getBaseUrl()}/offers`);

                if (response.ok) {
                    const data = await response.json();
                    result.innerHTML = `
                        <div class="success">
                            <strong>✅ GET /offers successful!</strong><br>
                            Status: ${response.status}<br>
                            Offers count: ${Array.isArray(data) ? data.length : 'Not an array'}<br>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    result.innerHTML = `
                        <div class="error">
                            <strong>❌ GET /offers failed!</strong><br>
                            Status: ${response.status}<br>
                            Status Text: ${response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        <strong>❌ Network error!</strong><br>
                        Error: ${error.message}<br>
                        <em>This is expected if the backend is not running.</em>
                    </div>
                `;
            }
        }

        async function testGetOffer() {
            const result = document.getElementById('apiResult');
            const testOfferId = 'offer_1704067200000';
            result.innerHTML = `<div class="info">Testing GET /offers/${testOfferId}...</div>`;

            try {
                const response = await fetch(`${getBaseUrl()}/offers/${testOfferId}`);

                if (response.ok) {
                    const data = await response.json();
                    result.innerHTML = `
                        <div class="success">
                            <strong>✅ GET /offers/${testOfferId} successful!</strong><br>
                            Status: ${response.status}<br>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    result.innerHTML = `
                        <div class="error">
                            <strong>❌ GET /offers/${testOfferId} failed!</strong><br>
                            Status: ${response.status}<br>
                            Status Text: ${response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        <strong>❌ Network error!</strong><br>
                        Error: ${error.message}<br>
                        <em>This is expected if the backend is not running.</em>
                    </div>
                `;
            }
        }

        async function testCreateOffer() {
            const result = document.getElementById('apiResult');
            result.innerHTML = '<div class="info">Testing POST /offers...</div>';

            const testOffer = {
                offerId: 'test_offer_' + Date.now(),
                title: 'Test Offer',
                serviceType: 'flight',
                description: 'Test offer created by API test',
                offerType: 'percentage_discount',
                availability: {
                    validFrom: '2024-01-01',
                    validTo: '2024-12-31',
                    bookingDeadline: '2024-11-30'
                },
                offerDetails: {
                    discountPercentage: 10,
                    maxDiscountCap: 100
                },
                targeting: {
                    serviceType: 'flight',
                    countries: ['US', 'CA']
                },
                metadata: {
                    createdAt: new Date().toISOString(),
                    status: 'active',
                    version: '1.0'
                }
            };

            try {
                const response = await fetch(`${getBaseUrl()}/offers`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testOffer)
                });

                if (response.ok) {
                    const data = await response.json();
                    result.innerHTML = `
                        <div class="success">
                            <strong>✅ POST /offers successful!</strong><br>
                            Status: ${response.status}<br>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    result.innerHTML = `
                        <div class="error">
                            <strong>❌ POST /offers failed!</strong><br>
                            Status: ${response.status}<br>
                            Status Text: ${response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        <strong>❌ Network error!</strong><br>
                        Error: ${error.message}<br>
                        <em>This is expected if the backend is not running.</em>
                    </div>
                `;
            }
        }

        function testFormPopulation() {
            const result = document.getElementById('formResult');

            // Test data
            const testOfferData = {
                title: 'Test Offer Title',
                serviceType: 'flight',
                description: 'Test description',
                offerType: 'percentage_discount',
                availability: {
                    validFrom: '2024-01-01',
                    validTo: '2024-12-31',
                    bookingDeadline: '2024-11-30'
                },
                offerDetails: {
                    discountPercentage: 15,
                    maxDiscountCap: 200
                }
            };

            result.innerHTML = `
                <div class="info">
                    <strong>Form Population Test</strong><br>
                    This test verifies that the form population logic works correctly.<br>
                    Test data: <pre>${JSON.stringify(testOfferData, null, 2)}</pre>
                    <br>
                    <strong>To test:</strong><br>
                    1. Open offers.html?edit=offer_1704067200000<br>
                    2. Check that the form fields are populated correctly<br>
                    3. Verify that the form doesn't get cleared after population<br>
                    <br>
                    <a href="offers.html?edit=offer_1704067200000" target="_blank">
                        <button>Open Edit Form Test</button>
                    </a>
                </div>
            `;
        }

        function testLoadingState() {
            const result = document.getElementById('loadingResult');

            result.innerHTML = `
                <div class="info">
                    <strong>Loading State Test</strong><br>
                    This test verifies that the loading overlay appears and disappears correctly.<br>
                    <br>
                    <strong>Issues Fixed:</strong><br>
                    ✅ Removed duplicate showLoadingState functions<br>
                    ✅ Unified loading overlay creation and removal<br>
                    ✅ Fixed hideLoadingState not removing overlay<br>
                    <br>
                    <strong>To test manually:</strong><br>
                    1. Open offers.html?edit=offer_1704067200000<br>
                    2. Watch for loading overlay to appear<br>
                    3. Verify it disappears after form loads<br>
                    4. Check that form is interactive after loading<br>
                    <br>
                    <a href="offers.html?edit=offer_1704067200000" target="_blank">
                        <button>Test Loading State</button>
                    </a>
                </div>
            `;
        }

        // Auto-run config test on page load
        document.addEventListener('DOMContentLoaded', function() {
            testConfig();
        });
    </script>
</body>
</html>
