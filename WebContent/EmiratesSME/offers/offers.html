<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Offer Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f9f9f9;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: #030303;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .form-container {
            padding: 40px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .form-section {
            background: #f9f9f9;
            padding: 25px;
            border-radius: 15px;
            border-left: 4px solid #d71921;
        }

        .form-section.full-width {
            grid-column: 1 / -1;
        }

        .form-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #d71921;
            box-shadow: 0 0 0 3px rgba(215, 25, 33, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        .targeting-section {
            background: #f9f9f9;
            border-left: 4px solid #d71921;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            display: none;
        }

        .targeting-section.active {
            display: block;
        }

        .targeting-section h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .multi-select {
            position: relative;
        }

        .multi-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .multi-select-dropdown.active {
            display: block;
        }

        .multi-select-option {
            padding: 10px 15px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .multi-select-option:hover {
            background-color: #f8f9fa;
        }

        .multi-select-option.selected {
            background-color: #d71921;
            color: white;
        }

        .selected-items {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 10px;
        }

        .selected-item {
            background: #d71921;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .remove-item {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-weight: bold;
        }

        .offer-type-details {
            background: #f9f9f9;
            border-left: 4px solid #d71921;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            display: none;
        }

        .offer-type-details.active {
            display: block;
        }

        .offer-type-details h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            margin-top: 40px;
            justify-content: center;
        }

        .btn {
            padding: 15px 40px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .btn-primary {
            background: #d71921;
            color: white;
            border: none;
        }

        .btn-primary:hover {
            background: #b8161c;
        }

        .btn-secondary {
            background: #f9f9f9;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .json-output {
            background: #333;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            border-left: 4px solid #d71921;
        }

        .json-output pre {
            margin: 0;
            white-space: pre-wrap;
        }

        .success-message {
            background: #f9f9f9;
            color: #333;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #d71921;
            display: none;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-row,
            .form-row-3 {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }

        .time-range {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .ancillary-service-group {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .ancillary-service-group h5 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .form-description {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 20px;
            font-style: italic;
        }

        .search-input {
            position: relative;
        }

        .search-input input {
            padding-left: 35px;
        }

        .search-input::before {
            content: "🔍";
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1;
            pointer-events: none;
        }

        .regional-groups {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
        }

        .regional-groups h6 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .load-factor-range {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .loyalty-status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .loyalty-badge {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .loyalty-badge.blue {
            background: #e3f2fd;
            color: #1976d2;
        }

        .loyalty-badge.gold {
            background: #fff8e1;
            color: #f57c00;
        }

        .loyalty-badge.diamond {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .loyalty-badge.platinum {
            background: #e8f5e8;
            color: #388e3c;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>✈️ Travel Offer Management</h1>
            <p>Create targeted travel offers with advanced filtering options</p>
        </div>

        <div class="form-container">
            <form id="offerForm">
                <div class="form-grid">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3>📋 Basic Information</h3>
                        <div class="form-group">
                            <label for="offerTitle">Offer Title</label>
                            <input type="text" id="offerTitle" name="offerTitle" placeholder="Amazing Flight Deal"
                                required>
                        </div>
                        <div class="form-group">
                            <label for="serviceType">Service Type</label>
                            <select id="serviceType" name="serviceType" required onchange="toggleTargetingSection()">
                                <option value="">Select Service Type</option>
                                <option value="flight">Flight</option>
                                <option value="hotel">Hotel</option>
                                <option value="package">Package Deal</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description"
                                placeholder="Describe your offer..."></textarea>
                        </div>
                    </div>

                    <!-- Offer Type & Details -->
                    <div class="form-section">
                        <h3>🎯 Offer Type & Value</h3>
                        <div class="form-group">
                            <label for="offerType">Offer Type</label>
                            <select id="offerType" name="offerType" required onchange="toggleOfferTypeDetails()">
                                <option value="">Select Offer Type</option>
                                <option value="loyalty_multiplier">Loyalty Multiplier</option>
                                <option value="bonus_points">Bonus Loyalty Points</option>
                                <option value="business_rewards_multiplier">Business Rewards Multiplier</option>
                                <option value="bonus_business_rewards">Bonus Business Rewards</option>
                                <option value="absolute_discount">Absolute Discount</option>
                                <option value="percentage_discount">Percentage Discount</option>
                                <option value="additional_nights" id="additionalNightsOption" style="display: none;">
                                    Additional Nights (Hotels Only)</option>
                                <option value="ancillary">Ancillary Services</option>
                            </select>
                        </div>

                        <!-- Offer Type Specific Details -->
                        <div id="loyaltyMultiplierDetails" class="offer-type-details">
                            <h4>Loyalty Multiplier Settings</h4>
                            <div class="form-group">
                                <label for="multiplierValue">Multiplier Value (e.g., 2x, 3x)</label>
                                <input type="number" id="multiplierValue" name="multiplierValue" step="0.1" min="1"
                                    placeholder="2">
                            </div>
                        </div>

                        <div id="bonusPointsDetails" class="offer-type-details">
                            <h4>Bonus Points Settings</h4>
                            <div class="form-group">
                                <label for="bonusPoints">Bonus Points Amount</label>
                                <input type="number" id="bonusPoints" name="bonusPoints" min="1" placeholder="1000">
                            </div>
                        </div>

                        <div id="businessRewardsMultiplierDetails" class="offer-type-details">
                            <h4>Business Rewards Multiplier Settings</h4>
                            <div class="form-group">
                                <label for="businessRewardsMultiplierValue">Multiplier Value (e.g., 2x, 3x)</label>
                                <input type="number" id="businessRewardsMultiplierValue"
                                    name="businessRewardsMultiplierValue" step="0.1" min="1" placeholder="2">
                            </div>
                        </div>

                        <div id="bonusBusinessRewardsDetails" class="offer-type-details">
                            <h4>Bonus Business Rewards Settings</h4>
                            <div class="form-group">
                                <label for="bonusBusinessRewards">Bonus Business Rewards Amount</label>
                                <input type="number" id="bonusBusinessRewards" name="bonusBusinessRewards" min="1"
                                    placeholder="500">
                            </div>
                        </div>

                        <div id="absoluteDiscountDetails" class="offer-type-details">
                            <h4>Absolute Discount Settings</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="discountAmount">Discount Amount</label>
                                    <input type="number" id="discountAmount" name="discountAmount" step="0.01"
                                        placeholder="50.00">
                                </div>
                                <div class="form-group">
                                    <label for="discountCurrency">Currency</label>
                                    <select id="discountCurrency" name="discountCurrency">
                                        <option value="USD">USD</option>
                                        <option value="EUR">EUR</option>
                                        <option value="GBP">GBP</option>
                                        <option value="INR">INR</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="percentageDiscountDetails" class="offer-type-details">
                            <h4>Percentage Discount Settings</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="discountPercentage">Discount Percentage</label>
                                    <input type="number" id="discountPercentage" name="discountPercentage" min="1"
                                        max="100" placeholder="15">
                                </div>
                                <div class="form-group">
                                    <label for="discountCap">Maximum Discount Cap</label>
                                    <input type="number" id="discountCap" name="discountCap" step="0.01"
                                        placeholder="200.00">
                                </div>
                            </div>
                        </div>

                        <div id="additionalNightsDetails" class="offer-type-details">
                            <h4>Additional Nights Settings</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="minimumStay">Minimum Nights to Book</label>
                                    <input type="number" id="minimumStay" name="minimumStay" min="1" placeholder="3">
                                </div>
                                <div class="form-group">
                                    <label for="freeNights">Free Nights Offered</label>
                                    <input type="number" id="freeNights" name="freeNights" min="1" placeholder="1">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Example: Book 3 nights, get 1 night free</label>
                            </div>
                        </div>

                        <div id="ancillaryDetails" class="offer-type-details">
                            <h4>Ancillary Services Settings</h4>
                            <p class="form-description">Select the complimentary services included with this offer.
                                Available options depend on the service type.</p>

                            <!-- Flight Ancillary Services -->
                            <div id="flightAncillaryServices" class="ancillary-service-group">
                                <h5>✈️ Flight Ancillary Services</h5>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="freeSeat" name="flightAncillary" value="free_seat">
                                        <label for="freeSeat">Free Seat Selection</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="loungeAccess" name="flightAncillary"
                                            value="lounge_access">
                                        <label for="loungeAccess">Airport Lounge Access</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="airportTransfer" name="flightAncillary"
                                            value="airport_transfer">
                                        <label for="airportTransfer">Airport Transfer</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="chauffeurDrive" name="flightAncillary"
                                            value="chauffeur_drive">
                                        <label for="chauffeurDrive">Chauffeur Drive</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="freeMeals" name="flightAncillary" value="free_meals">
                                        <label for="freeMeals">Free In-flight Meals</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="extraBaggage" name="flightAncillary"
                                            value="extra_baggage">
                                        <label for="extraBaggage">Extra Baggage Allowance</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="priorityBoarding" name="flightAncillary"
                                            value="priority_boarding">
                                        <label for="priorityBoarding">Priority Boarding</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="wifiAccess" name="flightAncillary"
                                            value="wifi_access">
                                        <label for="wifiAccess">Free Wi-Fi Access</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="fastTrackSecurity" name="flightAncillary"
                                            value="fast_track_security">
                                        <label for="fastTrackSecurity">Fast Track Security</label>
                                    </div>
                                </div>
                            </div>

                            <!-- Hotel Ancillary Services -->
                            <div id="hotelAncillaryServices" class="ancillary-service-group">
                                <h5>🏨 Hotel Ancillary Services</h5>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="earlyCheckin" name="hotelAncillary"
                                            value="early_checkin">
                                        <label for="earlyCheckin">Early Check-in</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="lateCheckout" name="hotelAncillary"
                                            value="late_checkout">
                                        <label for="lateCheckout">Late Check-out</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="freeBreakfast" name="hotelAncillary"
                                            value="free_breakfast">
                                        <label for="freeBreakfast">Free Breakfast</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="freeMealsHotel" name="hotelAncillary"
                                            value="free_meals">
                                        <label for="freeMealsHotel">Free Meals (Lunch/Dinner)</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="spaVisit" name="hotelAncillary" value="spa_visit">
                                        <label for="spaVisit">Complimentary Spa Visit</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="roomUpgrade" name="hotelAncillary"
                                            value="room_upgrade">
                                        <label for="roomUpgrade">Room Upgrade (Subject to Availability)</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="freeWifi" name="hotelAncillary" value="free_wifi">
                                        <label for="freeWifi">Free Wi-Fi</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="airportShuttle" name="hotelAncillary"
                                            value="airport_shuttle">
                                        <label for="airportShuttle">Free Airport Shuttle</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="gymAccess" name="hotelAncillary" value="gym_access">
                                        <label for="gymAccess">Free Gym/Fitness Center Access</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="poolAccess" name="hotelAncillary"
                                            value="pool_access">
                                        <label for="poolAccess">Pool Access</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="conciergeService" name="hotelAncillary"
                                            value="concierge_service">
                                        <label for="conciergeService">Personal Concierge Service</label>
                                    </div>
                                </div>
                            </div>

                            <!-- Package Deal Ancillary Services -->
                            <div id="packageAncillaryServices" class="ancillary-service-group">
                                <h5>📦 Package Deal Ancillary Services</h5>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="tourGuide" name="packageAncillary"
                                            value="tour_guide">
                                        <label for="tourGuide">Personal Tour Guide</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="cityTours" name="packageAncillary"
                                            value="city_tours">
                                        <label for="cityTours">Free City Tours</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="museumPasses" name="packageAncillary"
                                            value="museum_passes">
                                        <label for="museumPasses">Museum/Attraction Passes</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="transportPass" name="packageAncillary"
                                            value="transport_pass">
                                        <label for="transportPass">Local Transport Pass</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="travelInsurance" name="packageAncillary"
                                            value="travel_insurance">
                                        <label for="travelInsurance">Complimentary Travel Insurance</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Availability & Dates -->
                    <div class="form-section">
                        <h3>📅 Availability & Dates</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="validFrom">Valid From</label>
                                <input type="date" id="validFrom" name="validFrom" required>
                            </div>
                            <div class="form-group">
                                <label for="validTo">Valid To</label>
                                <input type="date" id="validTo" name="validTo" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="bookingDeadline">Booking Deadline</label>
                            <input type="date" id="bookingDeadline" name="bookingDeadline">
                        </div>
                    </div>
                </div>

                <!-- Flight Targeting Section -->
                <div id="flightTargeting" class="targeting-section">
                    <h4>✈️ Flight Targeting Options</h4>
                    <div class="form-grid">
                        <div class="form-section">
                            <h3>🛫 Route & Destination</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="departureCity">Departure City/Airport</label>
                                    <input type="text" id="departureCity" name="departureCity"
                                        placeholder="New York (NYC)">
                                </div>
                                <div class="form-group">
                                    <label for="arrivalCity">Arrival City/Airport</label>
                                    <input type="text" id="arrivalCity" name="arrivalCity" placeholder="London (LHR)">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="routeType">Route Type</label>
                                <select id="routeType" name="routeType">
                                    <option value="">Any Route</option>
                                    <option value="direct">Direct Flights Only</option>
                                    <option value="one_stop">One Stop Maximum</option>
                                    <option value="any">Any Route</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="destinations">Specific Destinations</label>
                                <textarea id="destinations" name="destinations"
                                    placeholder="Enter destinations separated by commas (e.g., Paris, Rome, Barcelona)"></textarea>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>⏰ Flight Timing</h3>
                            <div class="form-group">
                                <label for="departureTimeType">Departure Time Preference</label>
                                <select id="departureTimeType" name="departureTimeType">
                                    <option value="">Any Time</option>
                                    <option value="morning">Morning (06:00 - 12:00)</option>
                                    <option value="afternoon">Afternoon (12:00 - 18:00)</option>
                                    <option value="evening">Evening (18:00 - 24:00)</option>
                                    <option value="night">Night (00:00 - 06:00)</option>
                                    <option value="custom">Custom Time Range</option>
                                </select>
                            </div>
                            <div id="customTimeRange" class="time-range" style="display: none;">
                                <div class="form-group">
                                    <label for="departureFrom">Departure From</label>
                                    <input type="time" id="departureFrom" name="departureFrom">
                                </div>
                                <div class="form-group">
                                    <label for="departureTo">Departure To</label>
                                    <input type="time" id="departureTo" name="departureTo">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Days of Week</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="monday" name="daysOfWeek" value="monday">
                                        <label for="monday">Monday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="tuesday" name="daysOfWeek" value="tuesday">
                                        <label for="tuesday">Tuesday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="wednesday" name="daysOfWeek" value="wednesday">
                                        <label for="wednesday">Wednesday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="thursday" name="daysOfWeek" value="thursday">
                                        <label for="thursday">Thursday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="friday" name="daysOfWeek" value="friday">
                                        <label for="friday">Friday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="saturday" name="daysOfWeek" value="saturday">
                                        <label for="saturday">Saturday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="sunday" name="daysOfWeek" value="sunday">
                                        <label for="sunday">Sunday</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>🛋️ Cabin & Fare Class</h3>
                            <div class="form-group">
                                <label>Cabin Class</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="economy" name="cabinClass" value="economy">
                                        <label for="economy">Economy</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="premium_economy" name="cabinClass"
                                            value="premium_economy">
                                        <label for="premium_economy">Premium Economy</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="business" name="cabinClass" value="business">
                                        <label for="business">Business</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="first" name="cabinClass" value="first">
                                        <label for="first">First Class</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Fare Class</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="basic" name="fareClass" value="basic">
                                        <label for="basic">Basic/Restricted</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="standard" name="fareClass" value="standard">
                                        <label for="standard">Standard</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="flexible" name="fareClass" value="flexible">
                                        <label for="flexible">Flexible/Refundable</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="premium" name="fareClass" value="premium">
                                        <label for="premium">Premium/Flex</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hotel Targeting Section -->
                <div id="hotelTargeting" class="targeting-section">
                    <h4>🏨 Hotel Targeting Options</h4>
                    <div class="form-grid">
                        <div class="form-section">
                            <h3>📍 Location & Property</h3>
                            <div class="form-group">
                                <label for="hotelDestinations">Destinations</label>
                                <textarea id="hotelDestinations" name="hotelDestinations"
                                    placeholder="Enter destinations separated by commas (e.g., Paris, Rome, Barcelona)"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="specificProperties">Specific Properties</label>
                                <textarea id="specificProperties" name="specificProperties"
                                    placeholder="Enter hotel names separated by commas (e.g., Marriott Downtown, Hilton Garden Inn)"></textarea>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>🏢 Hotel Chains & Categories</h3>
                            <div class="form-group">
                                <label>Hotel Chains</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="marriott" name="hotelChains" value="marriott">
                                        <label for="marriott">Marriott</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="hilton" name="hotelChains" value="hilton">
                                        <label for="hilton">Hilton</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="hyatt" name="hotelChains" value="hyatt">
                                        <label for="hyatt">Hyatt</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="ihg" name="hotelChains" value="ihg">
                                        <label for="ihg">IHG</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="accor" name="hotelChains" value="accor">
                                        <label for="accor">Accor</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="radisson" name="hotelChains" value="radisson">
                                        <label for="radisson">Radisson</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="independent" name="hotelChains" value="independent">
                                        <label for="independent">Independent Hotels</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Hotel Star Rating</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="star3" name="starRating" value="3">
                                        <label for="star3">3 Star</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="star4" name="starRating" value="4">
                                        <label for="star4">4 Star</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="star5" name="starRating" value="5">
                                        <label for="star5">5 Star</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="luxury" name="starRating" value="luxury">
                                        <label for="luxury">Luxury</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- General Targeting Section -->
                <div id="generalTargeting" class="targeting-section active">
                    <h4>🎯 General Targeting Options</h4>
                    <div class="form-grid">
                        <div class="form-section">
                            <h3>⚙️ Advanced Filters</h3>
                            <div class="form-group">
                                <label for="loadFactor">Load Factor (%)</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="loadFactorMin">Minimum Load Factor</label>
                                        <input type="number" id="loadFactorMin" name="loadFactorMin" min="0" max="100"
                                            placeholder="70">
                                    </div>
                                    <div class="form-group">
                                        <label for="loadFactorMax">Maximum Load Factor</label>
                                        <input type="number" id="loadFactorMax" name="loadFactorMax" min="0" max="100"
                                            placeholder="90">
                                    </div>
                                </div>
                                <small>Target flights with specific load factor ranges (e.g., 70-90% for high-demand
                                    flights)</small>
                            </div>
                            <div class="form-group">
                                <label>Loyalty Status Targeting</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="loyaltyBlue" name="loyaltyStatus" value="blue">
                                        <label for="loyaltyBlue">Blue (Basic)</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="loyaltyGold" name="loyaltyStatus" value="gold">
                                        <label for="loyaltyGold">Gold</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="loyaltyDiamond" name="loyaltyStatus" value="diamond">
                                        <label for="loyaltyDiamond">Diamond</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="loyaltyPlatinum" name="loyaltyStatus"
                                            value="platinum">
                                        <label for="loyaltyPlatinum">Platinum</label>
                                    </div>
                                </div>
                                <small>Target users based on their Emirates Skywards loyalty status</small>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>🌍 Country Targeting</h3>
                            <div class="form-group">
                                <label>Regional Groups</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="regionMiddleEast" name="regions" value="middle_east"
                                            onchange="toggleRegionalCountries('middle_east')">
                                        <label for="regionMiddleEast">Middle East</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="regionSouthAsia" name="regions" value="south_asia"
                                            onchange="toggleRegionalCountries('south_asia')">
                                        <label for="regionSouthAsia">South Asia</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="regionAfrica" name="regions" value="africa"
                                            onchange="toggleRegionalCountries('africa')">
                                        <label for="regionAfrica">Africa</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="regionEurope" name="regions" value="europe"
                                            onchange="toggleRegionalCountries('europe')">
                                        <label for="regionEurope">Europe</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="regionFarEast" name="regions" value="far_east"
                                            onchange="toggleRegionalCountries('far_east')">
                                        <label for="regionFarEast">Far East</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="regionAuNz" name="regions" value="au_nz"
                                            onchange="toggleRegionalCountries('au_nz')">
                                        <label for="regionAuNz">AU/NZ</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="regionAmericas" name="regions" value="americas"
                                            onchange="toggleRegionalCountries('americas')">
                                        <label for="regionAmericas">Americas</label>
                                    </div>
                                </div>
                                <small>Select regional groups to automatically include all countries in those
                                    regions</small>
                            </div>
                            <div class="form-group">
                                <label for="countrySearch">Search Countries</label>
                                <div class="search-input">
                                    <input type="text" id="countrySearch" name="countrySearch"
                                        placeholder="Type to search countries..." onkeyup="filterCountries()">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="targetCountries">Target Countries</label>
                                <select id="targetCountries" name="targetCountries" multiple class="multi-select">
                                    <option value="US">United States</option>
                                    <option value="GB">United Kingdom</option>
                                    <option value="CA">Canada</option>
                                    <option value="AU">Australia</option>
                                    <option value="DE">Germany</option>
                                    <option value="FR">France</option>
                                    <option value="IT">Italy</option>
                                    <option value="ES">Spain</option>
                                    <option value="NL">Netherlands</option>
                                    <option value="CH">Switzerland</option>
                                    <option value="AT">Austria</option>
                                    <option value="BE">Belgium</option>
                                    <option value="SE">Sweden</option>
                                    <option value="NO">Norway</option>
                                    <option value="DK">Denmark</option>
                                    <option value="FI">Finland</option>
                                    <option value="IE">Ireland</option>
                                    <option value="PT">Portugal</option>
                                    <option value="GR">Greece</option>
                                    <option value="JP">Japan</option>
                                    <option value="KR">South Korea</option>
                                    <option value="SG">Singapore</option>
                                    <option value="HK">Hong Kong</option>
                                    <option value="AE">United Arab Emirates</option>
                                    <option value="SA">Saudi Arabia</option>
                                    <option value="IN">India</option>
                                    <option value="CN">China</option>
                                    <option value="BR">Brazil</option>
                                    <option value="MX">Mexico</option>
                                    <option value="AR">Argentina</option>
                                    <option value="ZA">South Africa</option>
                                    <option value="EG">Egypt</option>
                                    <option value="TR">Turkey</option>
                                    <option value="RU">Russia</option>
                                    <option value="PL">Poland</option>
                                    <option value="CZ">Czech Republic</option>
                                    <option value="HU">Hungary</option>
                                    <option value="RO">Romania</option>
                                    <option value="BG">Bulgaria</option>
                                    <option value="HR">Croatia</option>
                                    <option value="SI">Slovenia</option>
                                    <option value="SK">Slovakia</option>
                                    <option value="LT">Lithuania</option>
                                    <option value="LV">Latvia</option>
                                    <option value="EE">Estonia</option>
                                    <option value="MT">Malta</option>
                                    <option value="CY">Cyprus</option>
                                    <option value="LU">Luxembourg</option>
                                    <option value="IS">Iceland</option>
                                    <option value="NZ">New Zealand</option>
                                    <option value="TH">Thailand</option>
                                    <option value="MY">Malaysia</option>
                                    <option value="ID">Indonesia</option>
                                    <option value="PH">Philippines</option>
                                    <option value="VN">Vietnam</option>
                                    <option value="TW">Taiwan</option>
                                </select>
                                <small>Hold Ctrl/Cmd to select multiple countries. Leave empty to target all
                                    countries.</small>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>🏢 Company Targeting</h3>
                            <div class="form-group">
                                <label for="pccCodes">PCC (Pseudo City Code) Targeting</label>
                                <textarea id="pccCodes" name="pccCodes"
                                    placeholder="Enter PCC codes separated by commas (e.g., NYC123, LON456, DXB789)"
                                    rows="3"></textarea>
                                <small>Target companies based on their specific PCC codes</small>
                            </div>
                            <div class="form-group">
                                <label for="companySearch">Search Companies</label>
                                <div class="search-input">
                                    <input type="text" id="companySearch" name="companySearch"
                                        placeholder="Type to search companies..." onkeyup="filterCompanies()">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="targetCompanies">Target Companies</label>
                                <select id="targetCompanies" name="targetCompanies" multiple class="multi-select">
                                    <option value="microsoft">Microsoft Corporation</option>
                                    <option value="apple">Apple Inc.</option>
                                    <option value="google">Google LLC</option>
                                    <option value="amazon">Amazon.com Inc.</option>
                                    <option value="meta">Meta Platforms Inc.</option>
                                    <option value="tesla">Tesla Inc.</option>
                                    <option value="netflix">Netflix Inc.</option>
                                    <option value="salesforce">Salesforce Inc.</option>
                                    <option value="oracle">Oracle Corporation</option>
                                    <option value="ibm">IBM Corporation</option>
                                    <option value="intel">Intel Corporation</option>
                                    <option value="cisco">Cisco Systems Inc.</option>
                                    <option value="adobe">Adobe Inc.</option>
                                    <option value="nvidia">NVIDIA Corporation</option>
                                    <option value="paypal">PayPal Holdings Inc.</option>
                                    <option value="uber">Uber Technologies Inc.</option>
                                    <option value="airbnb">Airbnb Inc.</option>
                                    <option value="spotify">Spotify Technology S.A.</option>
                                    <option value="zoom">Zoom Video Communications</option>
                                    <option value="slack">Slack Technologies</option>
                                    <option value="dropbox">Dropbox Inc.</option>
                                    <option value="twitter">Twitter Inc.</option>
                                    <option value="linkedin">LinkedIn Corporation</option>
                                    <option value="snapchat">Snap Inc.</option>
                                    <option value="pinterest">Pinterest Inc.</option>
                                    <option value="square">Block Inc. (Square)</option>
                                    <option value="stripe">Stripe Inc.</option>
                                    <option value="shopify">Shopify Inc.</option>
                                    <option value="atlassian">Atlassian Corporation</option>
                                    <option value="servicenow">ServiceNow Inc.</option>
                                    <option value="workday">Workday Inc.</option>
                                    <option value="snowflake">Snowflake Inc.</option>
                                    <option value="databricks">Databricks Inc.</option>
                                    <option value="palantir">Palantir Technologies</option>
                                    <option value="crowdstrike">CrowdStrike Holdings</option>
                                    <option value="okta">Okta Inc.</option>
                                    <option value="twilio">Twilio Inc.</option>
                                    <option value="mongodb">MongoDB Inc.</option>
                                    <option value="elastic">Elastic N.V.</option>
                                    <option value="splunk">Splunk Inc.</option>
                                    <option value="vmware">VMware Inc.</option>
                                    <option value="redhat">Red Hat Inc.</option>
                                    <option value="docker">Docker Inc.</option>
                                    <option value="github">GitHub Inc.</option>
                                    <option value="gitlab">GitLab Inc.</option>
                                    <option value="jenkins">Jenkins</option>
                                    <option value="kubernetes">Kubernetes</option>
                                    <option value="aws">Amazon Web Services</option>
                                    <option value="azure">Microsoft Azure</option>
                                    <option value="gcp">Google Cloud Platform</option>
                                    <option value="digitalocean">DigitalOcean</option>
                                    <option value="linode">Linode</option>
                                    <option value="vultr">Vultr</option>
                                    <option value="heroku">Heroku</option>
                                    <option value="vercel">Vercel</option>
                                    <option value="netlify">Netlify</option>
                                    <option value="cloudflare">Cloudflare Inc.</option>
                                    <option value="fastly">Fastly Inc.</option>
                                    <option value="akamai">Akamai Technologies</option>
                                    <option value="other">Other Company</option>
                                </select>
                                <small>Hold Ctrl/Cmd to select multiple companies. Leave empty to target all
                                    companies.</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button type="button" class="btn btn-secondary" onclick="previewOffer()">
                        🔍 Preview JSON
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="goBackToListing()" id="backButton"
                        style="display: none;">
                        ← Back to List
                    </button>
                    <button type="submit" class="btn btn-primary">
                        💾 Save Offer
                    </button>
                </div>
            </form>

            <!-- Success Message -->
            <div id="successMessage" class="success-message">
                ✅ Offer saved successfully!
            </div>

            <!-- JSON Output -->
            <div id="jsonOutput" class="json-output" style="display: none;">
                <pre id="jsonContent"></pre>
            </div>
        </div>
    </div>

    <script src="../../js/trafla/config.js"></script>
    <script>
        // Global variables
        let isEditMode = false;
        let currentOfferId = null;
        let isLoading = false;

        // API utility functions
        function getBaseUrl() {
            return bizTravelBaseUrl || 'http://localhost:8080/TraflaBeta';
        }

        async function fetchOffer(offerId) {
            try {
                const response = await fetch(`${getBaseUrl()}/offers/${offerId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error('Error fetching offer:', error);
                throw error;
            }
        }

        async function saveOffer(offerData) {
            try {
                const url = isEditMode ?
                    `${getBaseUrl()}/offers/${currentOfferId}` :
                    `${getBaseUrl()}/offers`;

                const method = isEditMode ? 'POST' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(offerData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('Error saving offer:', error);
                throw error;
            }
        }

        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        function showLoadingState(message = 'Loading...') {
            // Create loading overlay
            const existingOverlay = document.getElementById('loadingOverlay');
            if (!existingOverlay) {
                const overlay = document.createElement('div');
                overlay.id = 'loadingOverlay';
                overlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.8); display: flex; align-items: center; justify-content: center; z-index: 9999; font-size: 1.2rem; color: #333;';
                overlay.innerHTML = `<div>🔄 ${message}</div>`;
                document.body.appendChild(overlay);
            }

            // Also dim the form container
            const container = document.querySelector('.form-container');
            if (container) {
                container.style.opacity = '0.6';
                container.style.pointerEvents = 'none';
            }
        }

        function hideLoadingState() {
            // Remove loading overlay
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.remove();
            }

            // Restore form container
            const container = document.querySelector('.form-container');
            if (container) {
                container.style.opacity = '1';
                container.style.pointerEvents = 'auto';
            }
        }

        // Initialize form with today's date and check for edit mode
        document.addEventListener('DOMContentLoaded', function () {
            // Check if we're in edit mode first
            const editOfferId = getUrlParameter('edit');
            if (editOfferId) {
                isEditMode = true;
                currentOfferId = editOfferId;

                // Update page title and button text
                document.querySelector('.header h1').textContent = '✏️ Edit Travel Offer';
                document.querySelector('.header p').textContent = 'Update your travel offer details';
                document.querySelector('.btn-primary').textContent = '💾 Update Offer';

                // Load offer data - this will populate all fields including dates
                loadOfferForEdit(editOfferId);
            } else {
                // Only set default dates for new offers
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('validFrom').value = today;

                const nextMonth = new Date();
                nextMonth.setMonth(nextMonth.getMonth() + 1);
                document.getElementById('validTo').value = nextMonth.toISOString().split('T')[0];
            }
        });

        async function loadOfferForEdit(offerId) {
            try {
                showLoadingState('Loading offer data...');

                const offerData = await fetchOffer(offerId);

                // Don't set default dates in edit mode - use the offer's dates
                populateFormWithOfferData(offerData.data);

                hideLoadingState();
            } catch (error) {
                console.error('Error loading offer for edit:', error);
                hideLoadingState();
                alert('Failed to load offer data. Please try again.');

                // Redirect back to list if offer can't be loaded
                window.location.href = 'offers-list.html';
            }
        }

        function populateFormWithOfferData(offerData) {
            // Basic information
            document.getElementById('offerTitle').value = offerData.title || '';
            document.getElementById('serviceType').value = offerData.serviceType || '';
            document.getElementById('description').value = offerData.description || '';
            document.getElementById('offerType').value = offerData.offerType || '';

            // Availability
            if (offerData.availability) {
                document.getElementById('validFrom').value = offerData.availability.validFrom || '';
                document.getElementById('validTo').value = offerData.availability.validTo || '';
                document.getElementById('bookingDeadline').value = offerData.availability.bookingDeadline || '';
            }

            // Trigger the targeting section display
            toggleTargetingSection();
            toggleOfferTypeDetails();

            // Populate offer details based on type
            if (offerData.offerDetails) {
                populateOfferDetails(offerData.offerType, offerData.offerDetails);
            }

            // Populate targeting data
            if (offerData.targeting) {
                populateTargetingData(offerData.targeting);
            }
        }

        // Removed duplicate function - using populateOfferDetails instead

        function toggleTargetingSection() {
            const serviceType = document.getElementById('serviceType').value;
            const flightTargeting = document.getElementById('flightTargeting');
            const hotelTargeting = document.getElementById('hotelTargeting');
            const additionalNightsOption = document.getElementById('additionalNightsOption');
            const offerTypeSelect = document.getElementById('offerType');

            // Hide all targeting sections
            flightTargeting.classList.remove('active');
            hotelTargeting.classList.remove('active');

            // Show/hide additional nights option based on service type
            if (serviceType === 'hotel') {
                additionalNightsOption.style.display = 'block';
                hotelTargeting.classList.add('active');
            } else {
                additionalNightsOption.style.display = 'none';
                // Reset offer type if additional nights was selected for non-hotel service
                if (offerTypeSelect.value === 'additional_nights') {
                    offerTypeSelect.value = '';
                    toggleOfferTypeDetails();
                }
                if (serviceType === 'flight') {
                    flightTargeting.classList.add('active');
                }
            }
        }

        function toggleOfferTypeDetails() {
            const offerType = document.getElementById('offerType').value;

            // Hide all offer type details
            document.querySelectorAll('.offer-type-details').forEach(section => {
                section.classList.remove('active');
            });

            // Show relevant offer type details
            const detailsMap = {
                'loyalty_multiplier': 'loyaltyMultiplierDetails',
                'bonus_points': 'bonusPointsDetails',
                'business_rewards_multiplier': 'businessRewardsMultiplierDetails',
                'bonus_business_rewards': 'bonusBusinessRewardsDetails',
                'absolute_discount': 'absoluteDiscountDetails',
                'percentage_discount': 'percentageDiscountDetails',
                'additional_nights': 'additionalNightsDetails',
                'ancillary': 'ancillaryDetails'
            };

            if (detailsMap[offerType]) {
                document.getElementById(detailsMap[offerType]).classList.add('active');

                // For ancillary offers, show/hide service-specific options
                if (offerType === 'ancillary') {
                    toggleAncillaryServiceGroups();
                }
            }
        }

        function toggleAncillaryServiceGroups() {
            const serviceType = document.getElementById('serviceType').value;
            const flightAncillary = document.getElementById('flightAncillaryServices');
            const hotelAncillary = document.getElementById('hotelAncillaryServices');
            const packageAncillary = document.getElementById('packageAncillaryServices');

            // Hide all ancillary service groups
            flightAncillary.style.display = 'none';
            hotelAncillary.style.display = 'none';
            packageAncillary.style.display = 'none';

            // Show relevant ancillary services based on service type
            switch (serviceType) {
                case 'flight':
                    flightAncillary.style.display = 'block';
                    break;
                case 'hotel':
                    hotelAncillary.style.display = 'block';
                    break;
                case 'package':
                    flightAncillary.style.display = 'block';
                    hotelAncillary.style.display = 'block';
                    packageAncillary.style.display = 'block';
                    break;
                default:
                    // Show all if no specific service type is selected
                    flightAncillary.style.display = 'block';
                    hotelAncillary.style.display = 'block';
                    packageAncillary.style.display = 'block';
                    break;
            }
        }

        // Toggle custom time range - with safety check
        function setupTimeRangeToggle() {
            const departureTimeType = document.getElementById('departureTimeType');
            if (departureTimeType) {
                departureTimeType.addEventListener('change', function () {
                    const customTimeRange = document.getElementById('customTimeRange');
                    if (customTimeRange) {
                        if (this.value === 'custom') {
                            customTimeRange.style.display = 'grid';
                        } else {
                            customTimeRange.style.display = 'none';
                        }
                    }
                });
            }
        }

        // Regional country mappings
        const regionalCountries = {
            'middle_east': ['AE', 'SA', 'QA', 'KW', 'BH', 'OM', 'JO', 'LB', 'IQ', 'IR', 'IL', 'PS', 'SY', 'YE'],
            'south_asia': ['IN', 'PK', 'BD', 'LK', 'NP', 'BT', 'MV', 'AF'],
            'africa': ['ZA', 'EG', 'NG', 'KE', 'ET', 'UG', 'TZ', 'GH', 'MG', 'CM', 'CI', 'NE', 'BF', 'ML', 'MW', 'ZM', 'SN', 'SO', 'TD', 'GN', 'RW', 'BJ', 'TN', 'BI', 'ER', 'SL', 'TG', 'CF', 'LR', 'MR', 'GM', 'BW', 'GA', 'LS', 'GW', 'GQ', 'MU', 'SZ', 'DJ', 'KM', 'CV', 'ST', 'SC'],
            'europe': ['GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'CH', 'AT', 'BE', 'SE', 'NO', 'DK', 'FI', 'IE', 'PT', 'GR', 'PL', 'CZ', 'HU', 'RO', 'BG', 'HR', 'SI', 'SK', 'LT', 'LV', 'EE', 'MT', 'CY', 'LU', 'IS'],
            'far_east': ['CN', 'JP', 'KR', 'TH', 'MY', 'SG', 'ID', 'PH', 'VN', 'TW', 'HK', 'MO', 'MM', 'KH', 'LA', 'BN', 'TL', 'MN'],
            'au_nz': ['AU', 'NZ', 'FJ', 'PG', 'NC', 'VU', 'SB', 'WS', 'KI', 'TO', 'FM', 'MH', 'PW', 'NR', 'TV'],
            'americas': ['US', 'CA', 'MX', 'BR', 'AR', 'CL', 'PE', 'CO', 'VE', 'EC', 'BO', 'PY', 'UY', 'GY', 'SR', 'GF', 'FK']
        };

        function toggleRegionalCountries(region) {
            const checkbox = document.getElementById(`region${region.charAt(0).toUpperCase() + region.slice(1).replace('_', '')}`);
            const countrySelect = document.getElementById('targetCountries');

            if (!countrySelect) return;

            const countries = regionalCountries[region] || [];

            if (checkbox.checked) {
                // Select all countries in this region
                countries.forEach(countryCode => {
                    const option = countrySelect.querySelector(`option[value="${countryCode}"]`);
                    if (option) option.selected = true;
                });
            } else {
                // Deselect all countries in this region
                countries.forEach(countryCode => {
                    const option = countrySelect.querySelector(`option[value="${countryCode}"]`);
                    if (option) option.selected = false;
                });
            }
        }

        function filterCountries() {
            const searchTerm = document.getElementById('countrySearch').value.toLowerCase();
            const countrySelect = document.getElementById('targetCountries');

            if (!countrySelect) return;

            const options = countrySelect.querySelectorAll('option');
            options.forEach(option => {
                const countryName = option.textContent.toLowerCase();
                const countryCode = option.value.toLowerCase();

                if (countryName.includes(searchTerm) || countryCode.includes(searchTerm)) {
                    option.style.display = 'block';
                } else {
                    option.style.display = 'none';
                }
            });
        }

        function filterCompanies() {
            const searchTerm = document.getElementById('companySearch').value.toLowerCase();
            const companySelect = document.getElementById('targetCompanies');

            if (!companySelect) return;

            const options = companySelect.querySelectorAll('option');
            options.forEach(option => {
                const companyName = option.textContent.toLowerCase();
                const companyValue = option.value.toLowerCase();

                if (companyName.includes(searchTerm) || companyValue.includes(searchTerm)) {
                    option.style.display = 'block';
                } else {
                    option.style.display = 'none';
                }
            });
        }

        function getCheckedValues(name) {
            const checkboxes = document.querySelectorAll(`input[name="${name}"]:checked`);
            return Array.from(checkboxes).map(cb => cb.value);
        }

        function getMultiSelectValues(selectId) {
            const select = document.getElementById(selectId);
            if (!select) return [];
            return Array.from(select.selectedOptions).map(option => option.value);
        }

        // Remove duplicate DOMContentLoaded - this will be handled by the first one

        function populateOfferDetails(offerType, details) {
            if (!details) return;

            switch (offerType) {
                case 'loyalty_multiplier':
                    document.getElementById('multiplierValue').value = details.multiplierValue || '';
                    break;
                case 'bonus_points':
                    document.getElementById('bonusPoints').value = details.bonusPoints || '';
                    break;
                case 'business_rewards_multiplier':
                    document.getElementById('businessRewardsMultiplierValue').value = details.businessRewardsMultiplierValue || '';
                    break;
                case 'bonus_business_rewards':
                    document.getElementById('bonusBusinessRewards').value = details.bonusBusinessRewards || '';
                    break;
                case 'absolute_discount':
                    document.getElementById('discountAmount').value = details.discountAmount || '';
                    document.getElementById('discountCurrency').value = details.currency || 'USD';
                    break;
                case 'percentage_discount':
                    document.getElementById('discountPercentage').value = details.discountPercentage || '';
                    document.getElementById('discountCap').value = details.maxDiscountCap || '';
                    break;
                case 'additional_nights':
                    document.getElementById('freeNights').value = details.freeNights || '';
                    document.getElementById('minimumStay').value = details.minimumStayRequired || '';
                    break;
                case 'ancillary':
                    // Populate ancillary services checkboxes
                    if (details.flightServices && Array.isArray(details.flightServices)) {
                        details.flightServices.forEach(service => {
                            const checkbox = document.querySelector(`input[name="flightAncillary"][value="${service}"]`);
                            if (checkbox) checkbox.checked = true;
                        });
                    }
                    if (details.hotelServices && Array.isArray(details.hotelServices)) {
                        details.hotelServices.forEach(service => {
                            const checkbox = document.querySelector(`input[name="hotelAncillary"][value="${service}"]`);
                            if (checkbox) checkbox.checked = true;
                        });
                    }
                    if (details.packageServices && Array.isArray(details.packageServices)) {
                        details.packageServices.forEach(service => {
                            const checkbox = document.querySelector(`input[name="packageAncillary"][value="${service}"]`);
                            if (checkbox) checkbox.checked = true;
                        });
                    }
                    break;
            }
        }

        function populateTargetingData(targeting) {
            if (targeting.serviceType === 'flight') {
                if (targeting.route) {
                    if (targeting.route.departureCity) document.getElementById('departureCity').value = targeting.route.departureCity;
                    if (targeting.route.arrivalCity) document.getElementById('arrivalCity').value = targeting.route.arrivalCity;
                    if (targeting.route.routeType) document.getElementById('routeType').value = targeting.route.routeType;
                    if (targeting.route.destinations) {
                        const destinations = Array.isArray(targeting.route.destinations) ?
                            targeting.route.destinations.join(', ') : targeting.route.destinations;
                        document.getElementById('destinations').value = destinations;
                    }
                }

                if (targeting.timing) {
                    if (targeting.timing.departureTimeType) {
                        document.getElementById('departureTimeType').value = targeting.timing.departureTimeType;

                        if (targeting.timing.departureTimeType === 'custom' && targeting.timing.customTimeRange) {
                            if (targeting.timing.customTimeRange.from) document.getElementById('departureFrom').value = targeting.timing.customTimeRange.from;
                            if (targeting.timing.customTimeRange.to) document.getElementById('departureTo').value = targeting.timing.customTimeRange.to;
                            document.getElementById('customTimeRange').style.display = 'grid';
                        }
                    }

                    if (targeting.timing.daysOfWeek && Array.isArray(targeting.timing.daysOfWeek)) {
                        targeting.timing.daysOfWeek.forEach(day => {
                            const checkbox = document.getElementById(day);
                            if (checkbox) checkbox.checked = true;
                        });
                    }
                }

                if (targeting.cabinClass && Array.isArray(targeting.cabinClass)) {
                    targeting.cabinClass.forEach(cabin => {
                        const checkbox = document.getElementById(cabin);
                        if (checkbox) checkbox.checked = true;
                    });
                }

                if (targeting.fareClass && Array.isArray(targeting.fareClass)) {
                    targeting.fareClass.forEach(fare => {
                        const checkbox = document.getElementById(fare);
                        if (checkbox) checkbox.checked = true;
                    });
                }
            } else if (targeting.serviceType === 'hotel') {
                if (targeting.location) {
                    if (targeting.location.destinations) {
                        const destinations = Array.isArray(targeting.location.destinations) ?
                            targeting.location.destinations.join(', ') : targeting.location.destinations;
                        document.getElementById('hotelDestinations').value = destinations;
                    }
                    if (targeting.location.specificProperties) {
                        const properties = Array.isArray(targeting.location.specificProperties) ?
                            targeting.location.specificProperties.join(', ') : targeting.location.specificProperties;
                        document.getElementById('specificProperties').value = properties;
                    }
                }

                if (targeting.hotelChains && Array.isArray(targeting.hotelChains)) {
                    targeting.hotelChains.forEach(chain => {
                        const checkbox = document.getElementById(chain);
                        if (checkbox) checkbox.checked = true;
                    });
                }

                if (targeting.starRating && Array.isArray(targeting.starRating)) {
                    targeting.starRating.forEach(rating => {
                        const checkbox = document.getElementById('star' + rating);
                        if (checkbox) checkbox.checked = true;
                    });
                }
            }

            // Populate general targeting options (countries and companies)
            if (targeting.countries && Array.isArray(targeting.countries)) {
                const countrySelect = document.getElementById('targetCountries');
                if (countrySelect) {
                    targeting.countries.forEach(country => {
                        const option = countrySelect.querySelector(`option[value="${country}"]`);
                        if (option) option.selected = true;
                    });
                }
            }

            if (targeting.companies && Array.isArray(targeting.companies)) {
                const companySelect = document.getElementById('targetCompanies');
                if (companySelect) {
                    targeting.companies.forEach(company => {
                        const option = companySelect.querySelector(`option[value="${company}"]`);
                        if (option) option.selected = true;
                    });
                }
            }

            // Populate loyalty status targeting
            if (targeting.loyaltyStatus && Array.isArray(targeting.loyaltyStatus)) {
                targeting.loyaltyStatus.forEach(status => {
                    const checkbox = document.getElementById(`loyalty${status.charAt(0).toUpperCase() + status.slice(1)}`);
                    if (checkbox) checkbox.checked = true;
                });
            }

            // Populate regional targeting
            if (targeting.regions && Array.isArray(targeting.regions)) {
                targeting.regions.forEach(region => {
                    const checkbox = document.getElementById(`region${region.charAt(0).toUpperCase() + region.slice(1).replace('_', '')}`);
                    if (checkbox) checkbox.checked = true;
                });
            }

            // Populate PCC codes
            if (targeting.pccCodes && Array.isArray(targeting.pccCodes)) {
                document.getElementById('pccCodes').value = targeting.pccCodes.join(', ');
            }

            // Populate load factor
            if (targeting.loadFactor) {
                if (targeting.loadFactor.min !== null) {
                    document.getElementById('loadFactorMin').value = targeting.loadFactor.min;
                }
                if (targeting.loadFactor.max !== null) {
                    document.getElementById('loadFactorMax').value = targeting.loadFactor.max;
                }
            }
        }

        function showError(message) {
            alert('Error: ' + message);
        }



        function generateOfferJSON() {
            const form = document.getElementById('offerForm');
            const formData = new FormData(form);

            const offer = {
                offerId: isEditMode ? currentOfferId : 'offer_' + Date.now(),
                title: formData.get('offerTitle'),
                serviceType: formData.get('serviceType'),
                description: formData.get('description'),
                offerType: formData.get('offerType'),
                availability: {
                    validFrom: formData.get('validFrom'),
                    validTo: formData.get('validTo'),
                    bookingDeadline: formData.get('bookingDeadline')
                },
                offerDetails: {},
                targeting: {},
                metadata: {
                    createdAt: isEditMode ? undefined : new Date().toISOString(),
                    updatedAt: isEditMode ? new Date().toISOString() : undefined,
                    status: 'active',
                    version: '1.0'
                }
            };

            if (offer.metadata.createdAt === undefined) delete offer.metadata.createdAt;
            if (offer.metadata.updatedAt === undefined) delete offer.metadata.updatedAt;

            const offerType = formData.get('offerType');
            switch (offerType) {
                case 'loyalty_multiplier':
                    offer.offerDetails = {
                        multiplierValue: parseFloat(formData.get('multiplierValue')) || 2
                    };
                    break;
                case 'bonus_points':
                    offer.offerDetails = {
                        bonusPoints: parseInt(formData.get('bonusPoints')) || 0
                    };
                    break;
                case 'business_rewards_multiplier':
                    offer.offerDetails = {
                        businessRewardsMultiplierValue: parseFloat(formData.get('businessRewardsMultiplierValue')) || 2
                    };
                    break;
                case 'bonus_business_rewards':
                    offer.offerDetails = {
                        bonusBusinessRewards: parseInt(formData.get('bonusBusinessRewards')) || 0
                    };
                    break;
                case 'absolute_discount':
                    offer.offerDetails = {
                        discountAmount: parseFloat(formData.get('discountAmount')) || 0,
                        currency: formData.get('discountCurrency') || 'USD'
                    };
                    break;
                case 'percentage_discount':
                    offer.offerDetails = {
                        discountPercentage: parseFloat(formData.get('discountPercentage')) || 0,
                        maxDiscountCap: parseFloat(formData.get('discountCap')) || null
                    };
                    break;
                case 'additional_nights':
                    offer.offerDetails = {
                        freeNights: parseInt(formData.get('freeNights')) || 1,
                        minimumStayRequired: parseInt(formData.get('minimumStay')) || 3
                    };
                    break;
                case 'ancillary':
                    offer.offerDetails = {
                        flightServices: getCheckedValues('flightAncillary'),
                        hotelServices: getCheckedValues('hotelAncillary'),
                        packageServices: getCheckedValues('packageAncillary')
                    };
                    break;
            }

            // Get general targeting options
            const targetCountries = getMultiSelectValues('targetCountries');
            const targetCompanies = getMultiSelectValues('targetCompanies');
            const loyaltyStatus = getCheckedValues('loyaltyStatus');
            const regions = getCheckedValues('regions');
            const pccCodes = formData.get('pccCodes') ?
                formData.get('pccCodes').split(',').map(code => code.trim()).filter(code => code) : [];

            // Load factor targeting
            const loadFactorMin = parseInt(formData.get('loadFactorMin')) || null;
            const loadFactorMax = parseInt(formData.get('loadFactorMax')) || null;

            const serviceType = formData.get('serviceType');
            if (serviceType === 'flight') {
                offer.targeting = {
                    serviceType: 'flight',
                    route: {
                        departureCity: formData.get('departureCity') || null,
                        arrivalCity: formData.get('arrivalCity') || null,
                        routeType: formData.get('routeType') || null,
                        destinations: formData.get('destinations') ?
                            formData.get('destinations').split(',').map(d => d.trim()) : []
                    },
                    timing: {
                        departureTimeType: formData.get('departureTimeType') || null,
                        customTimeRange: formData.get('departureTimeType') === 'custom' ? {
                            from: formData.get('departureFrom'),
                            to: formData.get('departureTo')
                        } : null,
                        daysOfWeek: getCheckedValues('daysOfWeek')
                    },
                    cabinClass: getCheckedValues('cabinClass'),
                    fareClass: getCheckedValues('fareClass'),
                    // Load factor targeting
                    loadFactor: (loadFactorMin !== null || loadFactorMax !== null) ? {
                        min: loadFactorMin,
                        max: loadFactorMax
                    } : null,
                    // General targeting options
                    countries: targetCountries,
                    companies: targetCompanies,
                    regions: regions,
                    pccCodes: pccCodes,
                    loyaltyStatus: loyaltyStatus
                };
            } else if (serviceType === 'hotel') {
                offer.targeting = {
                    serviceType: 'hotel',
                    location: {
                        destinations: formData.get('hotelDestinations') ?
                            formData.get('hotelDestinations').split(',').map(d => d.trim()) : [],
                        specificProperties: formData.get('specificProperties') ?
                            formData.get('specificProperties').split(',').map(p => p.trim()) : []
                    },
                    hotelChains: getCheckedValues('hotelChains'),
                    starRating: getCheckedValues('starRating'),
                    // General targeting options
                    countries: targetCountries,
                    companies: targetCompanies,
                    regions: regions,
                    pccCodes: pccCodes,
                    loyaltyStatus: loyaltyStatus
                };
            } else {
                // For package deals or when no specific service type is selected
                offer.targeting = {
                    serviceType: serviceType || 'general',
                    countries: targetCountries,
                    companies: targetCompanies,
                    regions: regions,
                    pccCodes: pccCodes,
                    loyaltyStatus: loyaltyStatus
                };
            }

            return offer;
        }

        function previewOffer() {
            const offer = generateOfferJSON();
            const jsonOutput = document.getElementById('jsonOutput');
            const jsonContent = document.getElementById('jsonContent');

            jsonContent.textContent = JSON.stringify(offer, null, 2);
            jsonOutput.style.display = 'block';
            jsonOutput.scrollIntoView({ behavior: 'smooth' });
        }

        async function submitOffer(offerData) {
            console.log(isEditMode ? 'Updating offer:' : 'Creating offer:', offerData);

            try {
                const result = await saveOffer(offerData);
                return {
                    success: true,
                    offerId: result.offerId || offerData.offerId,
                    message: isEditMode ? 'Offer updated successfully' : 'Offer created successfully',
                    data: result
                };
            } catch (error) {
                console.error('Error submitting offer:', error);
                throw error;
            }
        }

        document.getElementById('offerForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            const offer = generateOfferJSON();

            try {
                const button = document.querySelector('.btn-primary');
                button.textContent = isEditMode ? '⏳ Updating...' : '⏳ Saving...';
                button.disabled = true;

                const result = await submitOffer(offer);

                if (result.success) {
                    document.getElementById('successMessage').textContent =
                        '✅ ' + (isEditMode ? 'Offer updated successfully!' : 'Offer saved successfully!');
                    document.getElementById('successMessage').style.display = 'block';
                    document.getElementById('successMessage').scrollIntoView({ behavior: 'smooth' });

                    previewOffer();

                    setTimeout(() => {
                        const message = isEditMode ?
                            'Offer updated successfully! Would you like to continue editing or return to the offers list?' :
                            'Offer saved successfully! Would you like to create another offer?';

                        if (confirm(message)) {
                            if (isEditMode) {
                                return;
                            } else {
                                document.getElementById('offerForm').reset();
                                document.getElementById('jsonOutput').style.display = 'none';
                                document.getElementById('successMessage').style.display = 'none';
                                document.querySelectorAll('.targeting-section').forEach(section => {
                                    section.classList.remove('active');
                                });
                                document.querySelectorAll('.offer-type-details').forEach(section => {
                                    section.classList.remove('active');
                                });
                            }
                        } else {
                            window.location.href = 'offers-list.html';
                        }
                    }, 3000);
                }
            } catch (error) {
                alert('Error saving offer: ' + error.message);
            } finally {
                const button = document.querySelector('.btn-primary');
                button.textContent = isEditMode ? '💾 Update Offer' : '💾 Save Offer';
                button.disabled = false;
            }
        });
    </script>
</body>

</html>